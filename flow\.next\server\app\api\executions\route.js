/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/executions/route";
exports.ids = ["app/api/executions/route"];
exports.modules = {

/***/ "(rsc)/./app/api/executions/route.ts":
/*!*************************************!*\
  !*** ./app/api/executions/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Mock data for when API connections fail\nconst mockN8nExecutions = [\n    {\n        id: \"n8n-exec-1\",\n        workflowId: \"wf-1\",\n        workflowName: \"Email Processor\",\n        engine: \"n8n\",\n        status: \"success\",\n        duration: \"2.3s\",\n        startTime: new Date(Date.now() - 1000 * 60 * 5).toLocaleDateString(\"de-DE\", {\n            day: \"2-digit\",\n            month: \"2-digit\",\n            year: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            second: \"2-digit\"\n        }).replace(\",\", \"\"),\n        triggerType: \"webhook\",\n        folderId: \"unassigned\",\n        executionData: {\n            data: {\n                resultData: {\n                    runData: {\n                        Webhook: [\n                            {\n                                data: {\n                                    body: {\n                                        email: \"<EMAIL>\"\n                                    }\n                                },\n                                executionTime: 120\n                            }\n                        ],\n                        \"Process Email\": [\n                            {\n                                data: {\n                                    output: \"Email processed\"\n                                },\n                                executionTime: 350\n                            }\n                        ]\n                    }\n                }\n            }\n        }\n    },\n    {\n        id: \"n8n-exec-2\",\n        workflowId: \"wf-3\",\n        workflowName: \"Lead Scoring\",\n        engine: \"n8n\",\n        status: \"error\",\n        duration: \"1.1s\",\n        startTime: new Date(Date.now() - 1000 * 60 * 15).toLocaleDateString(\"de-DE\", {\n            day: \"2-digit\",\n            month: \"2-digit\",\n            year: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            second: \"2-digit\"\n        }).replace(\",\", \"\"),\n        triggerType: \"webhook\",\n        folderId: \"marketing\",\n        executionData: {\n            data: {\n                resultData: {\n                    error: {\n                        message: \"Failed to process lead data\"\n                    }\n                }\n            }\n        }\n    },\n    {\n        id: \"n8n-exec-3\",\n        workflowId: \"wf-6\",\n        workflowName: \"Report Generator\",\n        engine: \"n8n\",\n        status: \"running\",\n        duration: \"Running...\",\n        startTime: new Date(Date.now() - 1000 * 30).toLocaleDateString(\"de-DE\", {\n            day: \"2-digit\",\n            month: \"2-digit\",\n            year: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            second: \"2-digit\"\n        }).replace(\",\", \"\"),\n        triggerType: \"schedule\",\n        folderId: \"data-processing\",\n        executionData: {\n            data: {\n                resultData: {\n                    runData: {\n                        \"Data Fetch\": [\n                            {\n                                data: {\n                                    records: 500\n                                },\n                                executionTime: 1200\n                            }\n                        ]\n                    }\n                }\n            }\n        }\n    }\n];\nconst mockLangflowExecutions = [\n    {\n        id: \"langflow-exec-1\",\n        workflowId: \"wf-5\",\n        workflowName: \"ETL Pipeline\",\n        engine: \"langflow\",\n        status: \"success\",\n        duration: \"45.2s\",\n        startTime: new Date(Date.now() - 1000 * 60 * 10).toLocaleDateString(\"de-DE\", {\n            day: \"2-digit\",\n            month: \"2-digit\",\n            year: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            second: \"2-digit\"\n        }).replace(\",\", \"\"),\n        triggerType: \"schedule\",\n        folderId: \"data-processing\",\n        executionData: {\n            outputs: {\n                \"Data Source\": {\n                    status: \"success\",\n                    data: {\n                        records: 1250\n                    }\n                },\n                Transform: {\n                    status: \"success\",\n                    data: {\n                        transformations: [\n                            \"join\",\n                            \"filter\"\n                        ]\n                    }\n                }\n            }\n        }\n    },\n    {\n        id: \"langflow-exec-2\",\n        workflowId: \"wf-2\",\n        workflowName: \"Data Sync\",\n        engine: \"langflow\",\n        status: \"success\",\n        duration: \"12.7s\",\n        startTime: new Date(Date.now() - 1000 * 60 * 20).toLocaleDateString(\"de-DE\", {\n            day: \"2-digit\",\n            month: \"2-digit\",\n            year: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            second: \"2-digit\"\n        }).replace(\",\", \"\"),\n        triggerType: \"manual\",\n        folderId: \"unassigned\",\n        executionData: {\n            outputs: {\n                Sync: {\n                    status: \"success\",\n                    data: {\n                        synced: true\n                    }\n                }\n            }\n        }\n    },\n    {\n        id: \"langflow-exec-3\",\n        workflowId: \"wf-4\",\n        workflowName: \"Campaign Tracker\",\n        engine: \"langflow\",\n        status: \"error\",\n        duration: \"8.1s\",\n        startTime: new Date(Date.now() - 1000 * 60 * 25).toLocaleDateString(\"de-DE\", {\n            day: \"2-digit\",\n            month: \"2-digit\",\n            year: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            second: \"2-digit\"\n        }).replace(\",\", \"\"),\n        triggerType: \"webhook\",\n        folderId: \"marketing\",\n        executionData: {\n            outputs: {\n                \"Campaign Data\": {\n                    status: \"error\",\n                    error: \"API rate limit exceeded\"\n                }\n            },\n            error: \"API rate limit exceeded\"\n        }\n    }\n];\n// Create a timeout promise\nfunction createTimeoutPromise(ms) {\n    return new Promise((_, reject)=>{\n        setTimeout(()=>reject(new Error(\"Request timeout\")), ms);\n    });\n}\n// Fetch with timeout\nasync function fetchWithTimeout(url, options, timeoutMs = 5000) {\n    try {\n        const fetchPromise = fetch(url, options);\n        const timeoutPromise = createTimeoutPromise(timeoutMs);\n        return await Promise.race([\n            fetchPromise,\n            timeoutPromise\n        ]);\n    } catch (error) {\n        throw error;\n    }\n}\n// n8n API integration with improved error handling\nasync function fetchN8nExecutions() {\n    // Check if environment variables are set\n    const n8nBaseUrl = process.env.N8N_BASE_URL;\n    const n8nApiKey = process.env.N8N_API_KEY;\n    console.log(\"N8N Configuration:\", {\n        baseUrl: n8nBaseUrl ? \"Set\" : \"Not set\",\n        apiKey: n8nApiKey ? \"Set\" : \"Not set\"\n    });\n    if (!n8nBaseUrl || !n8nApiKey) {\n        console.log(\"N8N environment variables not configured, using mock data\");\n        return mockN8nExecutions;\n    }\n    try {\n        const url = `${n8nBaseUrl}/rest/executions`;\n        console.log(`Attempting to fetch n8n executions from: ${url}`);\n        const response = await fetchWithTimeout(url, {\n            headers: {\n                Authorization: `Bearer ${n8nApiKey}`,\n                \"Content-Type\": \"application/json\"\n            }\n        }, 5000) // 5 second timeout\n        ;\n        console.log(`N8N API Response Status: ${response.status}`);\n        if (!response.ok) {\n            throw new Error(`n8n API error: ${response.status} ${response.statusText}`);\n        }\n        const data = await response.json();\n        console.log(`N8N API returned ${data.data?.length || 0} executions`);\n        return data.data.map((execution)=>({\n                id: execution.id,\n                workflowId: execution.workflowId,\n                workflowName: execution.workflowData?.name || \"Unknown Workflow\",\n                engine: \"n8n\",\n                status: execution.finished ? execution.stoppedAt ? \"success\" : \"error\" : \"running\",\n                duration: execution.finished ? `${((new Date(execution.stoppedAt).getTime() - new Date(execution.startedAt).getTime()) / 1000).toFixed(1)}s` : \"Running...\",\n                startTime: new Date(execution.startedAt).toLocaleDateString(\"de-DE\", {\n                    day: \"2-digit\",\n                    month: \"2-digit\",\n                    year: \"numeric\",\n                    hour: \"2-digit\",\n                    minute: \"2-digit\",\n                    second: \"2-digit\"\n                }).replace(\",\", \"\"),\n                triggerType: execution.mode || \"manual\",\n                folderId: execution.workflowData?.tags?.[0] || \"unassigned\",\n                executionData: execution\n            }));\n    } catch (error) {\n        console.error(\"Error fetching n8n executions:\", error);\n        console.log(\"Falling back to mock n8n execution data\");\n        return mockN8nExecutions;\n    }\n}\n// Langflow API integration with improved error handling\nasync function fetchLangflowExecutions() {\n    // Check if environment variables are set\n    const langflowBaseUrl = process.env.LANGFLOW_BASE_URL;\n    const langflowApiKey = process.env.LANGFLOW_API_KEY;\n    console.log(\"Langflow Configuration:\", {\n        baseUrl: langflowBaseUrl ? \"Set\" : \"Not set\",\n        apiKey: langflowApiKey ? \"Set\" : \"Not set\"\n    });\n    if (!langflowBaseUrl || !langflowApiKey) {\n        console.log(\"Langflow environment variables not configured, using mock data\");\n        return mockLangflowExecutions;\n    }\n    try {\n        const url = `${langflowBaseUrl}/api/v1/runs`;\n        console.log(`Attempting to fetch Langflow executions from: ${url}`);\n        const response = await fetchWithTimeout(url, {\n            headers: {\n                Authorization: `Bearer ${langflowApiKey}`,\n                \"Content-Type\": \"application/json\"\n            }\n        }, 5000) // 5 second timeout\n        ;\n        console.log(`Langflow API Response Status: ${response.status}`);\n        if (!response.ok) {\n            throw new Error(`Langflow API error: ${response.status} ${response.statusText}`);\n        }\n        const data = await response.json();\n        console.log(`Langflow API returned ${data.runs?.length || 0} executions`);\n        return data.runs.map((run)=>({\n                id: run.id,\n                workflowId: run.flow_id,\n                workflowName: run.flow_name || \"Unknown Flow\",\n                engine: \"langflow\",\n                status: run.status === \"SUCCESS\" ? \"success\" : run.status === \"ERROR\" ? \"error\" : \"running\",\n                duration: run.duration ? `${run.duration.toFixed(1)}s` : \"N/A\",\n                startTime: new Date(run.timestamp).toLocaleDateString(\"de-DE\", {\n                    day: \"2-digit\",\n                    month: \"2-digit\",\n                    year: \"numeric\",\n                    hour: \"2-digit\",\n                    minute: \"2-digit\",\n                    second: \"2-digit\"\n                }).replace(\",\", \"\"),\n                triggerType: run.trigger_type || \"manual\",\n                folderId: run.tags?.[0] || \"unassigned\",\n                executionData: run\n            }));\n    } catch (error) {\n        console.error(\"Error fetching Langflow executions:\", error);\n        console.log(\"Falling back to mock Langflow execution data\");\n        return mockLangflowExecutions;\n    }\n}\nasync function GET() {\n    try {\n        console.log(\"=== Fetching executions from all sources ===\");\n        // Use Promise.allSettled to handle cases where one API fails but the other succeeds\n        const [n8nResult, langflowResult] = await Promise.allSettled([\n            fetchN8nExecutions(),\n            fetchLangflowExecutions()\n        ]);\n        // Extract results or use empty arrays for failed promises\n        const n8nExecutions = n8nResult.status === \"fulfilled\" ? n8nResult.value : mockN8nExecutions;\n        const langflowExecutions = langflowResult.status === \"fulfilled\" ? langflowResult.value : mockLangflowExecutions;\n        console.log(`N8N executions: ${n8nExecutions.length}`);\n        console.log(`Langflow executions: ${langflowExecutions.length}`);\n        const allExecutions = [\n            ...n8nExecutions,\n            ...langflowExecutions\n        ].sort((a, b)=>{\n            // Parse dates for proper comparison\n            const dateA = a.startTime.split(\" \")[0].split(\".\").reverse().join(\"-\") + \" \" + a.startTime.split(\" \")[1];\n            const dateB = b.startTime.split(\" \")[0].split(\".\").reverse().join(\"-\") + \" \" + b.startTime.split(\" \")[1];\n            return new Date(dateB).getTime() - new Date(dateA).getTime();\n        }).slice(0, 50) // Last 50 executions\n        ;\n        console.log(`Returning ${allExecutions.length} total executions`);\n        // Determine if we're using any mock data\n        const usingMockData = n8nResult.status === \"rejected\" || langflowResult.status === \"rejected\" || !process.env.N8N_BASE_URL || !process.env.N8N_API_KEY || !process.env.LANGFLOW_BASE_URL || !process.env.LANGFLOW_API_KEY;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            executions: allExecutions,\n            usingMockData,\n            message: usingMockData ? \"Using mock data due to API configuration or connection issues\" : \"Live data\"\n        });\n    } catch (error) {\n        console.error(\"Unexpected error in executions API route:\", error);\n        // Return mock data as ultimate fallback\n        const mockExecutions = [\n            ...mockN8nExecutions,\n            ...mockLangflowExecutions\n        ].sort((a, b)=>{\n            const dateA = a.startTime.split(\" \")[0].split(\".\").reverse().join(\"-\") + \" \" + a.startTime.split(\" \")[1];\n            const dateB = b.startTime.split(\" \")[0].split(\".\").reverse().join(\"-\") + \" \" + b.startTime.split(\" \")[1];\n            return new Date(dateB).getTime() - new Date(dateA).getTime();\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            executions: mockExecutions,\n            usingMockData: true,\n            message: \"Using mock data due to unexpected error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/executions/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexecutions%2Froute&page=%2Fapi%2Fexecutions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexecutions%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexecutions%2Froute&page=%2Fapi%2Fexecutions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexecutions%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Web_dev_AI_flow_app_api_executions_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/executions/route.ts */ \"(rsc)/./app/api/executions/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/executions/route\",\n        pathname: \"/api/executions\",\n        filename: \"route\",\n        bundlePath: \"app/api/executions/route\"\n    },\n    resolvedPagePath: \"C:\\\\Web_dev\\\\AI\\\\flow\\\\app\\\\api\\\\executions\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Web_dev_AI_flow_app_api_executions_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexecutions%2Froute&page=%2Fapi%2Fexecutions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexecutions%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexecutions%2Froute&page=%2Fapi%2Fexecutions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexecutions%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();