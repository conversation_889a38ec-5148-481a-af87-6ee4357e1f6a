version: '3.8'

services:
  # Redis for shared memory store
  redis:
    image: redis:7-alpine
    container_name: ai_system_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - ai_network

  # Main AI System Application
  ai_system:
    build: .
    container_name: ai_system_app
    ports:
      - "8000:8000"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - APP_HOST=0.0.0.0
      - APP_PORT=8000
      - DEBUG=True
      - LOG_LEVEL=INFO
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./static:/app/static
      - ./templates:/app/templates
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - ai_network
    restart: unless-stopped

  # Redis Commander for Redis management (optional)
  redis_commander:
    image: rediscommander/redis-commander:latest
    container_name: ai_system_redis_commander
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - ai_network
    profiles:
      - tools

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: ai_system_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - ai_system
    networks:
      - ai_network
    profiles:
      - production

volumes:
  redis_data:
    driver: local

networks:
  ai_network:
    driver: bridge
