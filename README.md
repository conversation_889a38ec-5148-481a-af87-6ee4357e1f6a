# Multi-Format Autonomous AI System

##  Overview

A sophisticated multi-agent system that processes inputs from **Email**, **JSON**, and **PDF** formats, classifies both format and business intent, routes to specialized agents, and dynamically chains follow-up actions based on extracted data.

##  Architecture

### Core Components

1. **Classifier Agent** - Advanced format and intent detection using few-shot learning
2. **Email Agent** - Email processing with tone analysis and escalation logic
3. **JSON Agent** - Schema validation and anomaly detection
4. **PDF Agent** - Real PDF parsing with compliance checking
5. **Shared Memory Store** - Redis-based centralized storage
6. **Action Router** - Dynamic action orchestration and execution

### System Flow

```
Input → Classifier → Specialized Agent → Action Router → Memory Store
  ↓         ↓              ↓               ↓            ↓
Format   Intent      Processing      Actions      Audit Trail
```

## Features

### Classifier Agent (Level-Up)
-  Detects format: JSON, Email, PDF
-  Identifies intent: RFQ, Complaint, Invoice, Regulation, Fraud Risk
-  Uses few-shot examples and schema matching
-  Passes routing metadata to memory

### Email Agent
-  Extracts structured fields (sender, urgency, issue/request)
-  Advanced tone analysis (polite, angry, threatening, escalation)
-  Sentiment scoring and entity extraction
-  Automated escalation logic
-  CRM integration simulation

### JSON Agent
-  Real-time schema validation
-  Sophisticated anomaly detection
-  Type checking and field validation
-  Security pattern detection (SQL injection, XSS)
-  Statistical anomaly analysis

### PDF Agent
-  Real PDF parsing (not hardcoded text)
-  Invoice line-item extraction
-  Compliance keyword detection (GDPR, FDA, HIPAA, SOX)
-  High-value transaction flagging
-  Document type classification

### Shared Memory Store
-  Redis-based centralized storage
-  Session tracking and audit trails
-  Agent state management
-  Performance metrics and counters

### Action Router
-  Dynamic action chaining
-  Retry logic and error handling
-  Multiple action types (escalate, flag, alert, ticket)
-  Priority-based routing
-  Simulated external API calls

##  Tech Stack

- **Backend**: Python 3.11 + FastAPI
- **Memory Store**: Redis
- **PDF Processing**: PyPDF2
- **ML/NLP**: LangChain, TextStat, NLTK
- **Validation**: Pydantic, JSONSchema
- **Frontend**: Bootstrap 5, Chart.js
- **Containerization**: Docker + Docker Compose

## Installation & Setup

### Prerequisites
- Python 3.11+
- Redis
- Docker (optional)

### Local Development

1. **Clone and setup**:
```bash
git clone <repository>
cd AI
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **Configure environment**:
```bash
cp .env.example .env
# Edit .env with your settings
```

3. **Start Redis**:
```bash
redis-server
```

4. **Run the application**:
```bash
python main.py
```

5. **Access the system**:
- Web Interface: http://localhost:8000
- API Documentation: http://localhost:8000/api/docs

### Docker Deployment

1. **Start with Docker Compose**:
```bash
docker-compose up -d
```

2. **View logs**:
```bash
docker-compose logs -f ai_system
```

3. **Stop services**:
```bash
docker-compose down
```

##  Testing

### Run Test Suite
```bash
python tests/test_system.py
```

### Generate Sample Data
```bash
python utils/sample_data.py
```

### API Testing
```bash
# Test email processing
curl -X POST "http://localhost:8000/api/process" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "From: <EMAIL>\nSubject: Complaint\n\nI am extremely disappointed with your service!",
    "content_type": "email"
  }'

# Test JSON processing
curl -X POST "http://localhost:8000/api/process" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "{\"event_type\": \"payment\", \"amount\": 999999, \"user_id\": \"test123\"}",
    "content_type": "json"
  }'
```

##  Monitoring & Analytics

### System Health
- **Endpoint**: `/api/health`
- **Metrics**: Agent status, memory store, action router

### Statistics
- **Endpoint**: `/api/stats`
- **Data**: Processing counts, format distribution, action metrics

### Session History
- **Endpoint**: `/api/sessions`
- **Features**: Audit trails, decision traces, action results

##  End-to-End Example

### Input: Angry Customer Email
```
From: <EMAIL>
Subject: Urgent - Defective Product

I am extremely angry about the defective product I received. 
This is unacceptable and I demand immediate action or I will 
contact my lawyer and the Better Business Bureau!
```

### Processing Flow:
1. **Classifier**: Format=Email, Intent=Complaint, Confidence=0.92
2. **Email Agent**: Tone=Angry, Urgency=High, Escalation=Required
3. **Action Router**: Creates escalation ticket in CRM
4. **Memory Store**: Logs complete audit trail

### Output Actions:
-  Escalation ticket created (ID: ESC-2024-001)
-  CRM notification sent
-  Manager alert triggered
-  Session logged for compliance

##  Web Interface Features

### Dashboard
- Real-time system health monitoring
- Processing statistics and charts
- Agent status indicators
- Performance metrics

### Content Processor
- Drag & drop file upload
- Text input with auto-detection
- Real-time processing feedback
- Detailed result visualization

### Analytics
- Session history with filtering
- Action execution tracking
- Anomaly detection reports
- Compliance flag monitoring

##  Configuration

### Environment Variables
```bash
# Core Settings
REDIS_HOST=localhost
REDIS_PORT=6379
APP_PORT=8000

# AI Configuration
OPENAI_API_KEY=your_key_here
CLASSIFICATION_CONFIDENCE_THRESHOLD=0.7

# External APIs
CRM_API_URL=https://api.crm.com
RISK_ALERT_API_URL=https://api.risk.com
```

### Agent Tuning
- Confidence thresholds
- Anomaly detection sensitivity
- Escalation criteria
- Compliance keywords

##  Performance

### Benchmarks
- **Email Processing**: ~200ms average
- **JSON Validation**: ~50ms average
- **PDF Parsing**: ~500ms average
- **Action Execution**: ~100ms average

### Scalability
- Concurrent request handling
- Redis-based session management
- Stateless agent design
- Horizontal scaling ready

## Security Features

### Data Protection
- Input sanitization
- SQL injection detection
- XSS pattern recognition
- Secure file handling

### Compliance
- GDPR keyword detection
- HIPAA compliance checking
- SOX financial controls
- FDA regulatory monitoring

##  Contributing

1. Fork the repository
2. Create feature branch
3. Add comprehensive tests
4. Update documentation
5. Submit pull request

##  License

MIT License - see LICENSE file for details

## Sos Support

- **Documentation**: See `/docs` folder
- **Issues**: GitHub Issues
- **API Docs**: http://localhost:8000/api/docs
- **Health Check**: http://localhost:8000/api/health

---

**Built with love for Flowbit AI Assessment**

*Demonstrating advanced multi-agent AI systems with real-world applications*
