import { NextRequest, NextResponse } from 'next/server';

// Mock detailed run data
const mockRunDetails: Record<string, any> = {
  'run_001': {
    id: 'run_001',
    flow_name: 'Email Agent',
    status: 'completed',
    duration: 2340,
    created_at: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
    completed_at: new Date(Date.now() - 1000 * 60 * 3).toISOString(),
    input_data: {
      content: `From: <EMAIL>
To: <EMAIL>
Subject: URGENT - Defective Product

I am extremely disappointed with your service! This is unacceptable!`
    },
    output_data: {
      classification: {
        format_type: 'email',
        business_intent: 'complaint',
        confidence: 0.92
      },
      analysis: {
        tone: 'angry',
        urgency_level: 'high',
        escalation_required: true,
        sentiment_score: -0.85
      },
      actions: [
        {
          action_type: 'escalate',
          priority: 'high',
          ticket_id: 'ESC-2024-001'
        }
      ]
    },
    node_details: [
      {
        node_id: 'input-1',
        node_name: 'Email Input',
        status: 'completed',
        duration: 45,
        input: { content: 'Email content...' },
        output: { processed: true }
      },
      {
        node_id: 'parser-1',
        node_name: '<PERSON><PERSON> Parser',
        status: 'completed',
        duration: 890,
        input: { email_content: 'Email content...' },
        output: { sender: '<EMAIL>', subject: 'URGENT - Defective Product' }
      },
      {
        node_id: 'analyzer-1',
        node_name: 'Tone Analyzer',
        status: 'completed',
        duration: 1200,
        input: { email_content: 'Email content...' },
        output: { tone: 'angry', sentiment_score: -0.85 }
      },
      {
        node_id: 'output-1',
        node_name: 'Email Analysis Output',
        status: 'completed',
        duration: 205,
        input: { combined_results: '...' },
        output: { final_result: 'Complete analysis' }
      }
    ],
    logs: [
      {
        timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
        level: 'info',
        message: 'Starting email processing workflow',
        node_id: 'input-1'
      },
      {
        timestamp: new Date(Date.now() - 1000 * 60 * 4.8).toISOString(),
        level: 'info',
        message: 'Email content received and validated',
        node_id: 'input-1'
      },
      {
        timestamp: new Date(Date.now() - 1000 * 60 * 4.7).toISOString(),
        level: 'info',
        message: 'Parsing email structure...',
        node_id: 'parser-1'
      },
      {
        timestamp: new Date(Date.now() - 1000 * 60 * 4.5).toISOString(),
        level: 'info',
        message: 'Email parsed successfully - sender: <EMAIL>',
        node_id: 'parser-1'
      },
      {
        timestamp: new Date(Date.now() - 1000 * 60 * 4.3).toISOString(),
        level: 'info',
        message: 'Analyzing tone and sentiment...',
        node_id: 'analyzer-1'
      },
      {
        timestamp: new Date(Date.now() - 1000 * 60 * 4.0).toISOString(),
        level: 'warning',
        message: 'Angry tone detected - escalation may be required',
        node_id: 'analyzer-1'
      },
      {
        timestamp: new Date(Date.now() - 1000 * 60 * 3.8).toISOString(),
        level: 'info',
        message: 'Sentiment analysis complete - score: -0.85',
        node_id: 'analyzer-1'
      },
      {
        timestamp: new Date(Date.now() - 1000 * 60 * 3.2).toISOString(),
        level: 'info',
        message: 'Combining analysis results...',
        node_id: 'output-1'
      },
      {
        timestamp: new Date(Date.now() - 1000 * 60 * 3.0).toISOString(),
        level: 'success',
        message: 'Email processing completed successfully',
        node_id: 'output-1'
      }
    ]
  },
  'run_002': {
    id: 'run_002',
    flow_name: 'JSON Agent',
    status: 'completed',
    duration: 1890,
    created_at: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
    completed_at: new Date(Date.now() - 1000 * 60 * 13).toISOString(),
    input_data: {
      content: '{"event_type": "payment", "amount": 999999.99, "user_id": "test123"}'
    },
    output_data: {
      validation: {
        is_valid_json: true,
        schema_type: 'webhook',
        confidence: 0.88
      },
      anomaly_analysis: {
        anomaly_score: 0.95,
        risk_level: 'critical',
        detected_anomalies: ['unusual_amount', 'suspicious_pattern']
      },
      recommended_actions: [
        {
          action_type: 'flag',
          priority: 'critical',
          reason: 'Extremely high transaction amount detected'
        }
      ]
    },
    node_details: [
      {
        node_id: 'input-1',
        node_name: 'JSON Input',
        status: 'completed',
        duration: 32,
        input: { content: 'JSON data...' },
        output: { validated: true }
      },
      {
        node_id: 'validator-1',
        node_name: 'JSON Validator',
        status: 'completed',
        duration: 567,
        input: { json_data: 'JSON data...' },
        output: { is_valid: true, schema_type: 'webhook' }
      },
      {
        node_id: 'anomaly-detector-1',
        node_name: 'Anomaly Detector',
        status: 'completed',
        duration: 1123,
        input: { json_data: 'JSON data...' },
        output: { anomaly_score: 0.95, risk_level: 'critical' }
      },
      {
        node_id: 'output-1',
        node_name: 'JSON Analysis Output',
        status: 'completed',
        duration: 168,
        input: { combined_results: '...' },
        output: { final_analysis: 'Complete' }
      }
    ],
    logs: [
      {
        timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
        level: 'info',
        message: 'Starting JSON validation and analysis',
        node_id: 'input-1'
      },
      {
        timestamp: new Date(Date.now() - 1000 * 60 * 14.8).toISOString(),
        level: 'info',
        message: 'JSON syntax validation passed',
        node_id: 'validator-1'
      },
      {
        timestamp: new Date(Date.now() - 1000 * 60 * 14.5).toISOString(),
        level: 'info',
        message: 'Schema identified as webhook type',
        node_id: 'validator-1'
      },
      {
        timestamp: new Date(Date.now() - 1000 * 60 * 14.2).toISOString(),
        level: 'info',
        message: 'Running anomaly detection algorithms...',
        node_id: 'anomaly-detector-1'
      },
      {
        timestamp: new Date(Date.now() - 1000 * 60 * 13.8).toISOString(),
        level: 'error',
        message: 'CRITICAL: Extremely high amount detected - $999,999.99',
        node_id: 'anomaly-detector-1'
      },
      {
        timestamp: new Date(Date.now() - 1000 * 60 * 13.5).toISOString(),
        level: 'warning',
        message: 'Anomaly score: 0.95 - flagging for review',
        node_id: 'anomaly-detector-1'
      },
      {
        timestamp: new Date(Date.now() - 1000 * 60 * 13.0).toISOString(),
        level: 'success',
        message: 'JSON analysis completed - high-risk transaction flagged',
        node_id: 'output-1'
      }
    ]
  }
};

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const runId = params.id;
    
    // In production, this would call the actual LangFlow API
    const langflowUrl = process.env.LANGFLOW_BASE_URL || 'http://localhost:7860';
    
    // For now, return mock data
    const runDetails = mockRunDetails[runId];
    
    if (!runDetails) {
      return NextResponse.json(
        { error: 'Run not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(runDetails);

  } catch (error) {
    console.error('Error fetching LangFlow run details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch run details' },
      { status: 500 }
    );
  }
}
