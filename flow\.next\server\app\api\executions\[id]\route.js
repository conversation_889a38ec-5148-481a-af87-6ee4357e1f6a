/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/executions/[id]/route";
exports.ids = ["app/api/executions/[id]/route"];
exports.modules = {

/***/ "(rsc)/./app/api/executions/[id]/route.ts":
/*!******************************************!*\
  !*** ./app/api/executions/[id]/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Mock execution details for when API connections fail\nconst mockExecutionDetails = {\n    n8n: {\n        \"n8n-exec-1\": {\n            id: \"n8n-exec-1\",\n            workflowData: {\n                name: \"Email Processor\"\n            },\n            finished: true,\n            stoppedAt: \"2024-01-15T14:30:25.000Z\",\n            startedAt: \"2024-01-15T14:30:22.000Z\",\n            mode: \"webhook\",\n            data: {\n                resultData: {\n                    runData: {\n                        Webhook: [\n                            {\n                                data: {\n                                    headers: {\n                                        \"content-type\": \"application/json\"\n                                    },\n                                    body: {\n                                        email: \"<EMAIL>\",\n                                        subject: \"Test Email\"\n                                    }\n                                },\n                                executionTime: 120\n                            }\n                        ],\n                        \"Process Email\": [\n                            {\n                                data: {\n                                    output: \"Email processed successfully\"\n                                },\n                                executionTime: 350\n                            }\n                        ]\n                    }\n                }\n            }\n        },\n        \"n8n-exec-2\": {\n            id: \"n8n-exec-2\",\n            workflowData: {\n                name: \"Lead Scoring\"\n            },\n            finished: true,\n            stoppedAt: \"2024-01-15T14:25:16.000Z\",\n            startedAt: \"2024-01-15T14:25:15.000Z\",\n            mode: \"webhook\",\n            data: {\n                resultData: {\n                    runData: {\n                        Webhook: [\n                            {\n                                data: {\n                                    headers: {\n                                        \"content-type\": \"application/json\"\n                                    },\n                                    body: {\n                                        lead: \"invalid data\"\n                                    }\n                                },\n                                executionTime: 110\n                            }\n                        ],\n                        \"Score Lead\": [\n                            {\n                                error: {\n                                    message: \"Failed to process lead data: Invalid format\"\n                                },\n                                executionTime: 200\n                            }\n                        ]\n                    },\n                    error: {\n                        message: \"Failed to process lead data: Invalid format\"\n                    }\n                }\n            }\n        },\n        \"n8n-exec-3\": {\n            id: \"n8n-exec-3\",\n            workflowData: {\n                name: \"Report Generator\"\n            },\n            finished: false,\n            startedAt: \"2024-01-15T14:35:10.000Z\",\n            mode: \"schedule\",\n            data: {\n                resultData: {\n                    runData: {\n                        \"Data Fetch\": [\n                            {\n                                data: {\n                                    records: 500,\n                                    status: \"processing\"\n                                },\n                                executionTime: 1200\n                            }\n                        ]\n                    }\n                }\n            }\n        }\n    },\n    langflow: {\n        \"langflow-exec-1\": {\n            id: \"langflow-exec-1\",\n            flow_name: \"ETL Pipeline\",\n            status: \"SUCCESS\",\n            timestamp: \"2024-01-15T14:20:08.000Z\",\n            duration: 45.2,\n            trigger_type: \"schedule\",\n            logs: [\n                {\n                    level: \"INFO\",\n                    timestamp: \"2024-01-15T14:20:08.000Z\",\n                    message: \"Flow execution started\"\n                },\n                {\n                    level: \"INFO\",\n                    timestamp: \"2024-01-15T14:20:53.000Z\",\n                    message: \"Flow execution completed\"\n                }\n            ],\n            outputs: {\n                \"Data Source\": {\n                    status: \"success\",\n                    data: {\n                        records: 1250,\n                        source: \"postgres://localhost:5432/source_db\"\n                    }\n                },\n                Transform: {\n                    status: \"success\",\n                    data: {\n                        transformations: [\n                            \"join\",\n                            \"filter\"\n                        ],\n                        records_processed: 1250\n                    }\n                }\n            }\n        },\n        \"langflow-exec-2\": {\n            id: \"langflow-exec-2\",\n            flow_name: \"Data Sync\",\n            status: \"SUCCESS\",\n            timestamp: \"2024-01-15T14:15:33.000Z\",\n            duration: 12.7,\n            trigger_type: \"manual\",\n            logs: [\n                {\n                    level: \"INFO\",\n                    timestamp: \"2024-01-15T14:15:33.000Z\",\n                    message: \"Flow execution started\"\n                },\n                {\n                    level: \"INFO\",\n                    timestamp: \"2024-01-15T14:15:46.000Z\",\n                    message: \"Flow execution completed\"\n                }\n            ],\n            outputs: {\n                Sync: {\n                    status: \"success\",\n                    data: {\n                        synced: true,\n                        records: 500\n                    }\n                }\n            }\n        },\n        \"langflow-exec-3\": {\n            id: \"langflow-exec-3\",\n            flow_name: \"Campaign Tracker\",\n            status: \"ERROR\",\n            timestamp: \"2024-01-15T14:10:15.000Z\",\n            duration: 8.1,\n            trigger_type: \"webhook\",\n            logs: [\n                {\n                    level: \"INFO\",\n                    timestamp: \"2024-01-15T14:10:15.000Z\",\n                    message: \"Flow execution started\"\n                },\n                {\n                    level: \"ERROR\",\n                    timestamp: \"2024-01-15T14:10:23.000Z\",\n                    message: \"API rate limit exceeded\"\n                }\n            ],\n            outputs: {\n                \"Campaign Data\": {\n                    status: \"error\",\n                    error: \"API rate limit exceeded\"\n                }\n            },\n            error: \"API rate limit exceeded\"\n        }\n    }\n};\n// Create a timeout promise\nfunction createTimeoutPromise(ms) {\n    return new Promise((_, reject)=>{\n        setTimeout(()=>reject(new Error(\"Request timeout\")), ms);\n    });\n}\n// Fetch with timeout\nasync function fetchWithTimeout(url, options, timeoutMs = 5000) {\n    try {\n        const fetchPromise = fetch(url, options);\n        const timeoutPromise = createTimeoutPromise(timeoutMs);\n        return await Promise.race([\n            fetchPromise,\n            timeoutPromise\n        ]);\n    } catch (error) {\n        throw error;\n    }\n}\nasync function fetchN8nExecutionDetails(executionId) {\n    // Check if environment variables are set\n    const n8nBaseUrl = process.env.N8N_BASE_URL;\n    const n8nApiKey = process.env.N8N_API_KEY;\n    if (!n8nBaseUrl || !n8nApiKey) {\n        console.log(\"N8N environment variables not configured, using mock data\");\n        return mockExecutionDetails.n8n[executionId] || null;\n    }\n    try {\n        const url = `${n8nBaseUrl}/rest/executions/${executionId}`;\n        console.log(`Fetching n8n execution details from: ${url}`);\n        const response = await fetchWithTimeout(url, {\n            headers: {\n                Authorization: `Bearer ${n8nApiKey}`,\n                \"Content-Type\": \"application/json\"\n            }\n        }, 5000);\n        if (!response.ok) {\n            throw new Error(`n8n API error: ${response.status} ${response.statusText}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching n8n execution details:\", error);\n        console.log(\"Using mock n8n execution details\");\n        return mockExecutionDetails.n8n[executionId] || null;\n    }\n}\nasync function fetchLangflowExecutionDetails(runId) {\n    // Check if environment variables are set\n    const langflowBaseUrl = process.env.LANGFLOW_BASE_URL;\n    const langflowApiKey = process.env.LANGFLOW_API_KEY;\n    if (!langflowBaseUrl || !langflowApiKey) {\n        console.log(\"Langflow environment variables not configured, using mock data\");\n        return mockExecutionDetails.langflow[runId] || null;\n    }\n    try {\n        const url = `${langflowBaseUrl}/api/v1/runs/${runId}`;\n        console.log(`Fetching Langflow execution details from: ${url}`);\n        const response = await fetchWithTimeout(url, {\n            headers: {\n                Authorization: `Bearer ${langflowApiKey}`,\n                \"Content-Type\": \"application/json\"\n            }\n        }, 5000);\n        if (!response.ok) {\n            throw new Error(`Langflow API error: ${response.status} ${response.statusText}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching Langflow execution details:\", error);\n        console.log(\"Using mock Langflow execution details\");\n        return mockExecutionDetails.langflow[runId] || null;\n    }\n}\nasync function GET(request, { params }) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const engine = searchParams.get(\"engine\");\n        const executionId = params.id;\n        console.log(`Fetching execution details for ID: ${executionId}, engine: ${engine}`);\n        let executionDetails = null;\n        if (engine === \"n8n\") {\n            executionDetails = await fetchN8nExecutionDetails(executionId);\n        } else if (engine === \"langflow\") {\n            executionDetails = await fetchLangflowExecutionDetails(executionId);\n        }\n        if (!executionDetails) {\n            console.log(`No execution details found for ID: ${executionId}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Execution not found\"\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            execution: executionDetails\n        });\n    } catch (error) {\n        console.error(\"Error fetching execution details:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch execution details\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/executions/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexecutions%2F%5Bid%5D%2Froute&page=%2Fapi%2Fexecutions%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexecutions%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexecutions%2F%5Bid%5D%2Froute&page=%2Fapi%2Fexecutions%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexecutions%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Web_dev_AI_flow_app_api_executions_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/executions/[id]/route.ts */ \"(rsc)/./app/api/executions/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/executions/[id]/route\",\n        pathname: \"/api/executions/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/executions/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Web_dev\\\\AI\\\\flow\\\\app\\\\api\\\\executions\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Web_dev_AI_flow_app_api_executions_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexecutions%2F%5Bid%5D%2Froute&page=%2Fapi%2Fexecutions%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexecutions%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexecutions%2F%5Bid%5D%2Froute&page=%2Fapi%2Fexecutions%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexecutions%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();