/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stats/route";
exports.ids = ["app/api/stats/route"];
exports.modules = {

/***/ "(rsc)/./app/api/stats/route.ts":
/*!********************************!*\
  !*** ./app/api/stats/route.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Mock stats data - in production this would come from database/analytics\nconst getSystemStats = ()=>{\n    const now = new Date();\n    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n    const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);\n    const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);\n    return {\n        timestamp: now.toISOString(),\n        period: {\n            current_time: now.toISOString(),\n            today: today.toISOString(),\n            week_start: thisWeek.toISOString(),\n            month_start: thisMonth.toISOString()\n        },\n        execution_counts: {\n            total: {\n                all_time: 15847,\n                today: 234,\n                this_week: 1456,\n                this_month: 6789\n            },\n            by_engine: {\n                langflow: {\n                    all_time: 8923,\n                    today: 156,\n                    this_week: 892,\n                    this_month: 3456\n                },\n                n8n: {\n                    all_time: 6924,\n                    today: 78,\n                    this_week: 564,\n                    this_month: 3333\n                }\n            },\n            by_agent: {\n                classifier: {\n                    all_time: 4234,\n                    today: 67,\n                    this_week: 423,\n                    this_month: 1678,\n                    success_rate: 0.95\n                },\n                email: {\n                    all_time: 3456,\n                    today: 45,\n                    this_week: 334,\n                    this_month: 1234,\n                    success_rate: 0.98\n                },\n                json: {\n                    all_time: 2890,\n                    today: 34,\n                    this_week: 267,\n                    this_month: 1089,\n                    success_rate: 0.92\n                },\n                pdf: {\n                    all_time: 2343,\n                    today: 10,\n                    this_week: 168,\n                    this_month: 789,\n                    success_rate: 0.89\n                }\n            }\n        },\n        format_processing: {\n            email: {\n                total_processed: 5678,\n                complaints_detected: 1234,\n                escalations_triggered: 456,\n                avg_sentiment_score: -0.23,\n                top_issues: [\n                    'billing',\n                    'delivery',\n                    'product_quality',\n                    'support_response'\n                ]\n            },\n            json: {\n                total_processed: 4567,\n                anomalies_detected: 234,\n                high_risk_transactions: 67,\n                avg_anomaly_score: 0.15,\n                validation_failures: 89\n            },\n            pdf: {\n                total_processed: 3456,\n                compliance_flags: 123,\n                high_value_documents: 45,\n                gdpr_documents: 78,\n                extraction_accuracy: 0.94\n            }\n        },\n        anomaly_detection: {\n            total_anomalies: 567,\n            high_severity: 89,\n            medium_severity: 234,\n            low_severity: 244,\n            false_positives: 23,\n            accuracy_rate: 0.91,\n            recent_patterns: [\n                {\n                    type: 'unusual_transaction_amount',\n                    count: 45,\n                    severity: 'high',\n                    last_detected: new Date(Date.now() - Math.random() * 3600000).toISOString()\n                },\n                {\n                    type: 'suspicious_email_pattern',\n                    count: 23,\n                    severity: 'medium',\n                    last_detected: new Date(Date.now() - Math.random() * 1800000).toISOString()\n                },\n                {\n                    type: 'compliance_violation',\n                    count: 12,\n                    severity: 'high',\n                    last_detected: new Date(Date.now() - Math.random() * 7200000).toISOString()\n                }\n            ]\n        },\n        compliance_tracking: {\n            gdpr_documents: 234,\n            hipaa_documents: 67,\n            sox_documents: 45,\n            pci_documents: 23,\n            compliance_score: 0.87,\n            violations_detected: 12,\n            remediation_required: 5\n        },\n        performance_metrics: {\n            avg_response_time: {\n                classifier: 1250,\n                email: 890,\n                json: 1100,\n                pdf: 2100\n            },\n            throughput: {\n                requests_per_minute: 45,\n                peak_rpm: 156,\n                avg_rpm_today: 67\n            },\n            error_rates: {\n                classifier: 0.05,\n                email: 0.02,\n                json: 0.08,\n                pdf: 0.11\n            }\n        },\n        real_time_metrics: {\n            active_executions: Math.floor(Math.random() * 10) + 1,\n            queue_length: Math.floor(Math.random() * 25),\n            cpu_usage: Math.floor(Math.random() * 30) + 20,\n            memory_usage: Math.floor(Math.random() * 40) + 30,\n            last_updated: now.toISOString()\n        },\n        trending: {\n            most_active_agent: 'classifier',\n            busiest_hour: '14:00',\n            peak_day: 'Tuesday',\n            growth_rate: {\n                daily: 0.12,\n                weekly: 0.08,\n                monthly: 0.15\n            }\n        }\n    };\n};\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const period = searchParams.get('period') || 'all';\n        const agent = searchParams.get('agent');\n        const format = searchParams.get('format');\n        let stats = getSystemStats();\n        // Filter by agent if specified\n        if (agent && stats.execution_counts.by_agent[agent]) {\n            stats = {\n                ...stats,\n                filtered_by: `agent:${agent}`,\n                agent_specific: stats.execution_counts.by_agent[agent]\n            };\n        }\n        // Filter by format if specified\n        if (format && stats.format_processing[format]) {\n            stats = {\n                ...stats,\n                filtered_by: `format:${format}`,\n                format_specific: stats.format_processing[format]\n            };\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(stats, {\n            headers: {\n                'Cache-Control': 'no-cache, no-store, must-revalidate',\n                'Pragma': 'no-cache',\n                'Expires': '0'\n            }\n        });\n    } catch (error) {\n        console.error('Stats fetch failed:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch stats',\n            timestamp: new Date().toISOString(),\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500,\n            headers: {\n                'Cache-Control': 'no-cache, no-store, must-revalidate'\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Froute&page=%2Fapi%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Froute&page=%2Fapi%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Web_dev_AI_flow_app_api_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/stats/route.ts */ \"(rsc)/./app/api/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stats/route\",\n        pathname: \"/api/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/stats/route\"\n    },\n    resolvedPagePath: \"C:\\\\Web_dev\\\\AI\\\\flow\\\\app\\\\api\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Web_dev_AI_flow_app_api_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Froute&page=%2Fapi%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Froute&page=%2Fapi%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();