/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/trigger/route";
exports.ids = ["app/api/trigger/route"];
exports.modules = {

/***/ "(rsc)/./app/api/trigger/route.ts":
/*!**********************************!*\
  !*** ./app/api/trigger/route.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Mock response for when API connections fail\nconst mockTriggerResponse = {\n    n8n: {\n        success: true,\n        executionId: \"mock-execution-id\",\n        message: \"Workflow triggered successfully (mock)\"\n    },\n    langflow: {\n        success: true,\n        run_id: \"mock-run-id\",\n        message: \"Flow triggered successfully (mock)\"\n    }\n};\n// Create a timeout promise\nfunction createTimeoutPromise(ms) {\n    return new Promise((_, reject)=>{\n        setTimeout(()=>reject(new Error(\"Request timeout\")), ms);\n    });\n}\n// Fetch with timeout\nasync function fetchWithTimeout(url, options, timeoutMs = 5000) {\n    try {\n        const fetchPromise = fetch(url, options);\n        const timeoutPromise = createTimeoutPromise(timeoutMs);\n        return await Promise.race([\n            fetchPromise,\n            timeoutPromise\n        ]);\n    } catch (error) {\n        throw error;\n    }\n}\nasync function triggerN8nWorkflow(workflowId, inputPayload) {\n    // Check if environment variables are set\n    const n8nBaseUrl = process.env.N8N_BASE_URL;\n    const n8nApiKey = process.env.N8N_API_KEY;\n    if (!n8nBaseUrl || !n8nApiKey) {\n        console.log(\"N8N environment variables not configured, using mock response\");\n        return mockTriggerResponse.n8n;\n    }\n    try {\n        const url = `${n8nBaseUrl}/rest/workflows/${workflowId}/run`;\n        console.log(`Triggering n8n workflow at: ${url}`);\n        const response = await fetchWithTimeout(url, {\n            method: \"POST\",\n            headers: {\n                Authorization: `Bearer ${n8nApiKey}`,\n                \"Content-Type\": \"application/json\"\n            }\n        }, 5000);\n        if (!response.ok) {\n            throw new Error(`n8n API error: ${response.status} ${response.statusText}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error triggering n8n workflow:\", error);\n        console.log(\"Using mock n8n trigger response\");\n        return mockTriggerResponse.n8n;\n    }\n}\nasync function triggerLangflowWorkflow(flowId, inputPayload, triggerType) {\n    // Check if environment variables are set\n    const langflowBaseUrl = process.env.LANGFLOW_BASE_URL;\n    const langflowApiKey = process.env.LANGFLOW_API_KEY;\n    if (!langflowBaseUrl || !langflowApiKey) {\n        console.log(\"Langflow environment variables not configured, using mock response\");\n        return mockTriggerResponse.langflow;\n    }\n    try {\n        const url = `${langflowBaseUrl}/api/v1/run/${flowId}`;\n        console.log(`Triggering Langflow workflow at: ${url}`);\n        const response = await fetchWithTimeout(url, {\n            method: \"POST\",\n            headers: {\n                Authorization: `Bearer ${langflowApiKey}`,\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                input_value: inputPayload?.content || \"Manual trigger from FlowBit Dashboard\",\n                input_type: \"chat\",\n                output_type: \"chat\",\n                trigger_type: triggerType || \"manual\",\n                ...inputPayload\n            })\n        }, 5000);\n        if (!response.ok) {\n            throw new Error(`Langflow API error: ${response.status} ${response.statusText}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error triggering Langflow workflow:\", error);\n        console.log(\"Using mock Langflow trigger response\");\n        return mockTriggerResponse.langflow;\n    }\n}\nasync function POST(request) {\n    try {\n        const { workflowId, engine, triggerType, inputPayload } = await request.json();\n        if (!workflowId || !engine) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Missing workflowId or engine\"\n            }, {\n                status: 400\n            });\n        }\n        console.log(`Triggering workflow: ${workflowId}, engine: ${engine}, trigger: ${triggerType}`);\n        let result = null;\n        if (engine === \"n8n\") {\n            result = await triggerN8nWorkflow(workflowId, inputPayload);\n        } else if (engine === \"langflow\") {\n            result = await triggerLangflowWorkflow(workflowId, inputPayload, triggerType);\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unsupported engine\"\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            result\n        });\n    } catch (error) {\n        console.error(\"Error triggering workflow:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to trigger workflow\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/trigger/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrigger%2Froute&page=%2Fapi%2Ftrigger%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrigger%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrigger%2Froute&page=%2Fapi%2Ftrigger%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrigger%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Web_dev_AI_flow_app_api_trigger_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/trigger/route.ts */ \"(rsc)/./app/api/trigger/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/trigger/route\",\n        pathname: \"/api/trigger\",\n        filename: \"route\",\n        bundlePath: \"app/api/trigger/route\"\n    },\n    resolvedPagePath: \"C:\\\\Web_dev\\\\AI\\\\flow\\\\app\\\\api\\\\trigger\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Web_dev_AI_flow_app_api_trigger_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrigger%2Froute&page=%2Fapi%2Ftrigger%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrigger%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrigger%2Froute&page=%2Fapi%2Ftrigger%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrigger%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();