import { formatDistance } from "./sk/_lib/formatDistance.mjs";
import { formatLong } from "./sk/_lib/formatLong.mjs";
import { formatRelative } from "./sk/_lib/formatRelative.mjs";
import { localize } from "./sk/_lib/localize.mjs";
import { match } from "./sk/_lib/match.mjs";

/**
 * @category Locales
 * @summary Slovak locale.
 * @language Slovak
 * @iso-639-2 slk
 * <AUTHOR> [@mareksuscak](https://github.com/mareksuscak)
 */
export const sk = {
  code: "sk",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default sk;
