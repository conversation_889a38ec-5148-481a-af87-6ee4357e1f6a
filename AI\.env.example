# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Application Configuration
APP_HOST=0.0.0.0
APP_PORT=8000
DEBUG=True

# External API Endpoints (for simulation)
CRM_API_URL=https://api.example.com/crm
RISK_ALERT_API_URL=https://api.example.com/risk
NOTIFICATION_API_URL=https://api.example.com/notify

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=./uploads

# Agent Configuration
CLASSIFICATION_CONFIDENCE_THRESHOLD=0.7
ANOMALY_DETECTION_THRESHOLD=0.8
COMPLIANCE_KEYWORDS=GDPR,FDA,HIPAA,SOX,PCI-DSS

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
