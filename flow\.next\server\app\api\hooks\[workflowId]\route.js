/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/hooks/[workflowId]/route";
exports.ids = ["app/api/hooks/[workflowId]/route"];
exports.modules = {

/***/ "(rsc)/./app/api/hooks/[workflowId]/route.ts":
/*!*********************************************!*\
  !*** ./app/api/hooks/[workflowId]/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Store for webhook executions (in production, use a proper database)\nconst webhookExecutions = {};\nasync function GET(request, { params }) {\n    try {\n        const workflowId = params.workflowId;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            webhook_url: `${\"http://localhost:3000\" || 0}/api/hooks/${workflowId}`,\n            workflow_id: workflowId,\n            method: 'POST',\n            content_type: 'application/json',\n            description: 'Public webhook endpoint for triggering LangFlow workflows',\n            recent_executions: webhookExecutions[workflowId]?.slice(-10) || []\n        });\n    } catch (error) {\n        console.error('Error getting webhook info:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to get webhook information'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request, { params }) {\n    try {\n        const workflowId = params.workflowId;\n        const body = await request.json();\n        // Log the webhook execution\n        const execution = {\n            id: `webhook_${Date.now()}`,\n            timestamp: new Date().toISOString(),\n            source_ip: request.headers.get('x-forwarded-for') || 'unknown',\n            user_agent: request.headers.get('user-agent') || 'unknown',\n            payload: body,\n            workflow_id: workflowId\n        };\n        if (!webhookExecutions[workflowId]) {\n            webhookExecutions[workflowId] = [];\n        }\n        webhookExecutions[workflowId].push(execution);\n        // Keep only last 100 executions per workflow\n        if (webhookExecutions[workflowId].length > 100) {\n            webhookExecutions[workflowId] = webhookExecutions[workflowId].slice(-100);\n        }\n        // Forward to LangFlow trigger API\n        const triggerResponse = await fetch(`${request.nextUrl.origin}/api/trigger`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                workflowId: workflowId,\n                engine: 'langflow',\n                triggerType: 'webhook',\n                inputPayload: body\n            })\n        });\n        if (!triggerResponse.ok) {\n            throw new Error(`Trigger API error: ${triggerResponse.status}`);\n        }\n        const triggerResult = await triggerResponse.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            execution_id: execution.id,\n            run_id: triggerResult.result?.run_id,\n            message: 'Webhook received and workflow triggered',\n            timestamp: execution.timestamp\n        });\n    } catch (error) {\n        console.error('Error processing webhook:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to process webhook',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n// Handle other HTTP methods\nasync function PUT(request, { params }) {\n    return POST(request, {\n        params\n    });\n}\nasync function PATCH(request, { params }) {\n    return POST(request, {\n        params\n    });\n}\nasync function DELETE(request, { params }) {\n    try {\n        const workflowId = params.workflowId;\n        // Clear webhook execution history\n        delete webhookExecutions[workflowId];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Webhook execution history cleared',\n            workflow_id: workflowId\n        });\n    } catch (error) {\n        console.error('Error clearing webhook history:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to clear webhook history'\n        }, {\n            status: 500\n        });\n    }\n}\n// Handle preflight requests for CORS\nasync function OPTIONS() {\n    return new Response(null, {\n        status: 200,\n        headers: {\n            'Access-Control-Allow-Origin': '*',\n            'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',\n            'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n            'Access-Control-Max-Age': '86400'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2hvb2tzL1t3b3JrZmxvd0lkXS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQXdEO0FBRXhELHNFQUFzRTtBQUN0RSxNQUFNQyxvQkFBMkMsQ0FBQztBQUUzQyxlQUFlQyxJQUNwQkMsT0FBb0IsRUFDcEIsRUFBRUMsTUFBTSxFQUFzQztJQUU5QyxJQUFJO1FBQ0YsTUFBTUMsYUFBYUQsT0FBT0MsVUFBVTtRQUVwQyxPQUFPTCxxREFBWUEsQ0FBQ00sSUFBSSxDQUFDO1lBQ3ZCQyxhQUFhLEdBQUdDLHVCQUFnQyxJQUFJLENBQXVCLENBQUMsV0FBVyxFQUFFSCxZQUFZO1lBQ3JHTSxhQUFhTjtZQUNiTyxRQUFRO1lBQ1JDLGNBQWM7WUFDZEMsYUFBYTtZQUNiQyxtQkFBbUJkLGlCQUFpQixDQUFDSSxXQUFXLEVBQUVXLE1BQU0sQ0FBQyxPQUFPLEVBQUU7UUFDcEU7SUFDRixFQUFFLE9BQU9DLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLCtCQUErQkE7UUFDN0MsT0FBT2pCLHFEQUFZQSxDQUFDTSxJQUFJLENBQ3RCO1lBQUVXLE9BQU87UUFBb0MsR0FDN0M7WUFBRUUsUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFTyxlQUFlQyxLQUNwQmpCLE9BQW9CLEVBQ3BCLEVBQUVDLE1BQU0sRUFBc0M7SUFFOUMsSUFBSTtRQUNGLE1BQU1DLGFBQWFELE9BQU9DLFVBQVU7UUFDcEMsTUFBTWdCLE9BQU8sTUFBTWxCLFFBQVFHLElBQUk7UUFFL0IsNEJBQTRCO1FBQzVCLE1BQU1nQixZQUFZO1lBQ2hCQyxJQUFJLENBQUMsUUFBUSxFQUFFQyxLQUFLQyxHQUFHLElBQUk7WUFDM0JDLFdBQVcsSUFBSUYsT0FBT0csV0FBVztZQUNqQ0MsV0FBV3pCLFFBQVEwQixPQUFPLENBQUNDLEdBQUcsQ0FBQyxzQkFBc0I7WUFDckRDLFlBQVk1QixRQUFRMEIsT0FBTyxDQUFDQyxHQUFHLENBQUMsaUJBQWlCO1lBQ2pERSxTQUFTWDtZQUNUVixhQUFhTjtRQUNmO1FBRUEsSUFBSSxDQUFDSixpQkFBaUIsQ0FBQ0ksV0FBVyxFQUFFO1lBQ2xDSixpQkFBaUIsQ0FBQ0ksV0FBVyxHQUFHLEVBQUU7UUFDcEM7UUFDQUosaUJBQWlCLENBQUNJLFdBQVcsQ0FBQzRCLElBQUksQ0FBQ1g7UUFFbkMsNkNBQTZDO1FBQzdDLElBQUlyQixpQkFBaUIsQ0FBQ0ksV0FBVyxDQUFDNkIsTUFBTSxHQUFHLEtBQUs7WUFDOUNqQyxpQkFBaUIsQ0FBQ0ksV0FBVyxHQUFHSixpQkFBaUIsQ0FBQ0ksV0FBVyxDQUFDVyxLQUFLLENBQUMsQ0FBQztRQUN2RTtRQUVBLGtDQUFrQztRQUNsQyxNQUFNbUIsa0JBQWtCLE1BQU1DLE1BQU0sR0FBR2pDLFFBQVFrQyxPQUFPLENBQUNDLE1BQU0sQ0FBQyxZQUFZLENBQUMsRUFBRTtZQUMzRTFCLFFBQVE7WUFDUmlCLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1lBQ0FSLE1BQU1rQixLQUFLQyxTQUFTLENBQUM7Z0JBQ25CbkMsWUFBWUE7Z0JBQ1pvQyxRQUFRO2dCQUNSQyxhQUFhO2dCQUNiQyxjQUFjdEI7WUFDaEI7UUFDRjtRQUVBLElBQUksQ0FBQ2MsZ0JBQWdCUyxFQUFFLEVBQUU7WUFDdkIsTUFBTSxJQUFJQyxNQUFNLENBQUMsbUJBQW1CLEVBQUVWLGdCQUFnQmhCLE1BQU0sRUFBRTtRQUNoRTtRQUVBLE1BQU0yQixnQkFBZ0IsTUFBTVgsZ0JBQWdCN0IsSUFBSTtRQUVoRCxPQUFPTixxREFBWUEsQ0FBQ00sSUFBSSxDQUFDO1lBQ3ZCeUMsU0FBUztZQUNUQyxjQUFjMUIsVUFBVUMsRUFBRTtZQUMxQjBCLFFBQVFILGNBQWNJLE1BQU0sRUFBRUQ7WUFDOUJFLFNBQVM7WUFDVHpCLFdBQVdKLFVBQVVJLFNBQVM7UUFDaEM7SUFFRixFQUFFLE9BQU9ULE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkE7UUFDM0MsT0FBT2pCLHFEQUFZQSxDQUFDTSxJQUFJLENBQ3RCO1lBQ0VXLE9BQU87WUFDUG1DLFNBQVNuQyxpQkFBaUI0QixRQUFRNUIsTUFBTWtDLE9BQU8sR0FBRztRQUNwRCxHQUNBO1lBQUVoQyxRQUFRO1FBQUk7SUFFbEI7QUFDRjtBQUVBLDRCQUE0QjtBQUNyQixlQUFla0MsSUFDcEJsRCxPQUFvQixFQUNwQixFQUFFQyxNQUFNLEVBQXNDO0lBRTlDLE9BQU9nQixLQUFLakIsU0FBUztRQUFFQztJQUFPO0FBQ2hDO0FBRU8sZUFBZWtELE1BQ3BCbkQsT0FBb0IsRUFDcEIsRUFBRUMsTUFBTSxFQUFzQztJQUU5QyxPQUFPZ0IsS0FBS2pCLFNBQVM7UUFBRUM7SUFBTztBQUNoQztBQUVPLGVBQWVtRCxPQUNwQnBELE9BQW9CLEVBQ3BCLEVBQUVDLE1BQU0sRUFBc0M7SUFFOUMsSUFBSTtRQUNGLE1BQU1DLGFBQWFELE9BQU9DLFVBQVU7UUFFcEMsa0NBQWtDO1FBQ2xDLE9BQU9KLGlCQUFpQixDQUFDSSxXQUFXO1FBRXBDLE9BQU9MLHFEQUFZQSxDQUFDTSxJQUFJLENBQUM7WUFDdkJ5QyxTQUFTO1lBQ1RJLFNBQVM7WUFDVHhDLGFBQWFOO1FBQ2Y7SUFDRixFQUFFLE9BQU9ZLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLG1DQUFtQ0E7UUFDakQsT0FBT2pCLHFEQUFZQSxDQUFDTSxJQUFJLENBQ3RCO1lBQUVXLE9BQU87UUFBa0MsR0FDM0M7WUFBRUUsUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFQSxxQ0FBcUM7QUFDOUIsZUFBZXFDO0lBQ3BCLE9BQU8sSUFBSUMsU0FBUyxNQUFNO1FBQ3hCdEMsUUFBUTtRQUNSVSxTQUFTO1lBQ1AsK0JBQStCO1lBQy9CLGdDQUFnQztZQUNoQyxnQ0FBZ0M7WUFDaEMsMEJBQTBCO1FBQzVCO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFdlYl9kZXZcXEFJXFxmbG93XFxhcHBcXGFwaVxcaG9va3NcXFt3b3JrZmxvd0lkXVxccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcblxuLy8gU3RvcmUgZm9yIHdlYmhvb2sgZXhlY3V0aW9ucyAoaW4gcHJvZHVjdGlvbiwgdXNlIGEgcHJvcGVyIGRhdGFiYXNlKVxuY29uc3Qgd2ViaG9va0V4ZWN1dGlvbnM6IFJlY29yZDxzdHJpbmcsIGFueVtdPiA9IHt9O1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKFxuICByZXF1ZXN0OiBOZXh0UmVxdWVzdCxcbiAgeyBwYXJhbXMgfTogeyBwYXJhbXM6IHsgd29ya2Zsb3dJZDogc3RyaW5nIH0gfVxuKSB7XG4gIHRyeSB7XG4gICAgY29uc3Qgd29ya2Zsb3dJZCA9IHBhcmFtcy53b3JrZmxvd0lkO1xuICAgIFxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICB3ZWJob29rX3VybDogYCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQkFTRV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMCd9L2FwaS9ob29rcy8ke3dvcmtmbG93SWR9YCxcbiAgICAgIHdvcmtmbG93X2lkOiB3b3JrZmxvd0lkLFxuICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICBjb250ZW50X3R5cGU6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnUHVibGljIHdlYmhvb2sgZW5kcG9pbnQgZm9yIHRyaWdnZXJpbmcgTGFuZ0Zsb3cgd29ya2Zsb3dzJyxcbiAgICAgIHJlY2VudF9leGVjdXRpb25zOiB3ZWJob29rRXhlY3V0aW9uc1t3b3JrZmxvd0lkXT8uc2xpY2UoLTEwKSB8fCBbXVxuICAgIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgd2ViaG9vayBpbmZvOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiAnRmFpbGVkIHRvIGdldCB3ZWJob29rIGluZm9ybWF0aW9uJyB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChcbiAgcmVxdWVzdDogTmV4dFJlcXVlc3QsXG4gIHsgcGFyYW1zIH06IHsgcGFyYW1zOiB7IHdvcmtmbG93SWQ6IHN0cmluZyB9IH1cbikge1xuICB0cnkge1xuICAgIGNvbnN0IHdvcmtmbG93SWQgPSBwYXJhbXMud29ya2Zsb3dJZDtcbiAgICBjb25zdCBib2R5ID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XG4gICAgXG4gICAgLy8gTG9nIHRoZSB3ZWJob29rIGV4ZWN1dGlvblxuICAgIGNvbnN0IGV4ZWN1dGlvbiA9IHtcbiAgICAgIGlkOiBgd2ViaG9va18ke0RhdGUubm93KCl9YCxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgc291cmNlX2lwOiByZXF1ZXN0LmhlYWRlcnMuZ2V0KCd4LWZvcndhcmRlZC1mb3InKSB8fCAndW5rbm93bicsXG4gICAgICB1c2VyX2FnZW50OiByZXF1ZXN0LmhlYWRlcnMuZ2V0KCd1c2VyLWFnZW50JykgfHwgJ3Vua25vd24nLFxuICAgICAgcGF5bG9hZDogYm9keSxcbiAgICAgIHdvcmtmbG93X2lkOiB3b3JrZmxvd0lkXG4gICAgfTtcbiAgICBcbiAgICBpZiAoIXdlYmhvb2tFeGVjdXRpb25zW3dvcmtmbG93SWRdKSB7XG4gICAgICB3ZWJob29rRXhlY3V0aW9uc1t3b3JrZmxvd0lkXSA9IFtdO1xuICAgIH1cbiAgICB3ZWJob29rRXhlY3V0aW9uc1t3b3JrZmxvd0lkXS5wdXNoKGV4ZWN1dGlvbik7XG4gICAgXG4gICAgLy8gS2VlcCBvbmx5IGxhc3QgMTAwIGV4ZWN1dGlvbnMgcGVyIHdvcmtmbG93XG4gICAgaWYgKHdlYmhvb2tFeGVjdXRpb25zW3dvcmtmbG93SWRdLmxlbmd0aCA+IDEwMCkge1xuICAgICAgd2ViaG9va0V4ZWN1dGlvbnNbd29ya2Zsb3dJZF0gPSB3ZWJob29rRXhlY3V0aW9uc1t3b3JrZmxvd0lkXS5zbGljZSgtMTAwKTtcbiAgICB9XG4gICAgXG4gICAgLy8gRm9yd2FyZCB0byBMYW5nRmxvdyB0cmlnZ2VyIEFQSVxuICAgIGNvbnN0IHRyaWdnZXJSZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke3JlcXVlc3QubmV4dFVybC5vcmlnaW59L2FwaS90cmlnZ2VyYCwge1xuICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICB9LFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICB3b3JrZmxvd0lkOiB3b3JrZmxvd0lkLFxuICAgICAgICBlbmdpbmU6ICdsYW5nZmxvdycsXG4gICAgICAgIHRyaWdnZXJUeXBlOiAnd2ViaG9vaycsXG4gICAgICAgIGlucHV0UGF5bG9hZDogYm9keVxuICAgICAgfSlcbiAgICB9KTtcbiAgICBcbiAgICBpZiAoIXRyaWdnZXJSZXNwb25zZS5vaykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBUcmlnZ2VyIEFQSSBlcnJvcjogJHt0cmlnZ2VyUmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgIH1cbiAgICBcbiAgICBjb25zdCB0cmlnZ2VyUmVzdWx0ID0gYXdhaXQgdHJpZ2dlclJlc3BvbnNlLmpzb24oKTtcbiAgICBcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIGV4ZWN1dGlvbl9pZDogZXhlY3V0aW9uLmlkLFxuICAgICAgcnVuX2lkOiB0cmlnZ2VyUmVzdWx0LnJlc3VsdD8ucnVuX2lkLFxuICAgICAgbWVzc2FnZTogJ1dlYmhvb2sgcmVjZWl2ZWQgYW5kIHdvcmtmbG93IHRyaWdnZXJlZCcsXG4gICAgICB0aW1lc3RhbXA6IGV4ZWN1dGlvbi50aW1lc3RhbXBcbiAgICB9KTtcbiAgICBcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwcm9jZXNzaW5nIHdlYmhvb2s6JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgXG4gICAgICAgIGVycm9yOiAnRmFpbGVkIHRvIHByb2Nlc3Mgd2ViaG9vaycsXG4gICAgICAgIGRldGFpbHM6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InXG4gICAgICB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuXG4vLyBIYW5kbGUgb3RoZXIgSFRUUCBtZXRob2RzXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUFVUKFxuICByZXF1ZXN0OiBOZXh0UmVxdWVzdCxcbiAgeyBwYXJhbXMgfTogeyBwYXJhbXM6IHsgd29ya2Zsb3dJZDogc3RyaW5nIH0gfVxuKSB7XG4gIHJldHVybiBQT1NUKHJlcXVlc3QsIHsgcGFyYW1zIH0pO1xufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUEFUQ0goXG4gIHJlcXVlc3Q6IE5leHRSZXF1ZXN0LFxuICB7IHBhcmFtcyB9OiB7IHBhcmFtczogeyB3b3JrZmxvd0lkOiBzdHJpbmcgfSB9XG4pIHtcbiAgcmV0dXJuIFBPU1QocmVxdWVzdCwgeyBwYXJhbXMgfSk7XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBERUxFVEUoXG4gIHJlcXVlc3Q6IE5leHRSZXF1ZXN0LFxuICB7IHBhcmFtcyB9OiB7IHBhcmFtczogeyB3b3JrZmxvd0lkOiBzdHJpbmcgfSB9XG4pIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB3b3JrZmxvd0lkID0gcGFyYW1zLndvcmtmbG93SWQ7XG4gICAgXG4gICAgLy8gQ2xlYXIgd2ViaG9vayBleGVjdXRpb24gaGlzdG9yeVxuICAgIGRlbGV0ZSB3ZWJob29rRXhlY3V0aW9uc1t3b3JrZmxvd0lkXTtcbiAgICBcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIG1lc3NhZ2U6ICdXZWJob29rIGV4ZWN1dGlvbiBoaXN0b3J5IGNsZWFyZWQnLFxuICAgICAgd29ya2Zsb3dfaWQ6IHdvcmtmbG93SWRcbiAgICB9KTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjbGVhcmluZyB3ZWJob29rIGhpc3Rvcnk6JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gY2xlYXIgd2ViaG9vayBoaXN0b3J5JyB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuXG4vLyBIYW5kbGUgcHJlZmxpZ2h0IHJlcXVlc3RzIGZvciBDT1JTXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gT1BUSU9OUygpIHtcbiAgcmV0dXJuIG5ldyBSZXNwb25zZShudWxsLCB7XG4gICAgc3RhdHVzOiAyMDAsXG4gICAgaGVhZGVyczoge1xuICAgICAgJ0FjY2Vzcy1Db250cm9sLUFsbG93LU9yaWdpbic6ICcqJyxcbiAgICAgICdBY2Nlc3MtQ29udHJvbC1BbGxvdy1NZXRob2RzJzogJ0dFVCwgUE9TVCwgUFVULCBQQVRDSCwgREVMRVRFLCBPUFRJT05TJyxcbiAgICAgICdBY2Nlc3MtQ29udHJvbC1BbGxvdy1IZWFkZXJzJzogJ0NvbnRlbnQtVHlwZSwgQXV0aG9yaXphdGlvbicsXG4gICAgICAnQWNjZXNzLUNvbnRyb2wtTWF4LUFnZSc6ICc4NjQwMCdcbiAgICB9XG4gIH0pO1xufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsIndlYmhvb2tFeGVjdXRpb25zIiwiR0VUIiwicmVxdWVzdCIsInBhcmFtcyIsIndvcmtmbG93SWQiLCJqc29uIiwid2ViaG9va191cmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQkFTRV9VUkwiLCJ3b3JrZmxvd19pZCIsIm1ldGhvZCIsImNvbnRlbnRfdHlwZSIsImRlc2NyaXB0aW9uIiwicmVjZW50X2V4ZWN1dGlvbnMiLCJzbGljZSIsImVycm9yIiwiY29uc29sZSIsInN0YXR1cyIsIlBPU1QiLCJib2R5IiwiZXhlY3V0aW9uIiwiaWQiLCJEYXRlIiwibm93IiwidGltZXN0YW1wIiwidG9JU09TdHJpbmciLCJzb3VyY2VfaXAiLCJoZWFkZXJzIiwiZ2V0IiwidXNlcl9hZ2VudCIsInBheWxvYWQiLCJwdXNoIiwibGVuZ3RoIiwidHJpZ2dlclJlc3BvbnNlIiwiZmV0Y2giLCJuZXh0VXJsIiwib3JpZ2luIiwiSlNPTiIsInN0cmluZ2lmeSIsImVuZ2luZSIsInRyaWdnZXJUeXBlIiwiaW5wdXRQYXlsb2FkIiwib2siLCJFcnJvciIsInRyaWdnZXJSZXN1bHQiLCJzdWNjZXNzIiwiZXhlY3V0aW9uX2lkIiwicnVuX2lkIiwicmVzdWx0IiwibWVzc2FnZSIsImRldGFpbHMiLCJQVVQiLCJQQVRDSCIsIkRFTEVURSIsIk9QVElPTlMiLCJSZXNwb25zZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/api/hooks/[workflowId]/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fhooks%2F%5BworkflowId%5D%2Froute&page=%2Fapi%2Fhooks%2F%5BworkflowId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhooks%2F%5BworkflowId%5D%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fhooks%2F%5BworkflowId%5D%2Froute&page=%2Fapi%2Fhooks%2F%5BworkflowId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhooks%2F%5BworkflowId%5D%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Web_dev_AI_flow_app_api_hooks_workflowId_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/hooks/[workflowId]/route.ts */ \"(rsc)/./app/api/hooks/[workflowId]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/hooks/[workflowId]/route\",\n        pathname: \"/api/hooks/[workflowId]\",\n        filename: \"route\",\n        bundlePath: \"app/api/hooks/[workflowId]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Web_dev\\\\AI\\\\flow\\\\app\\\\api\\\\hooks\\\\[workflowId]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Web_dev_AI_flow_app_api_hooks_workflowId_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fhooks%2F%5BworkflowId%5D%2Froute&page=%2Fapi%2Fhooks%2F%5BworkflowId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhooks%2F%5BworkflowId%5D%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fhooks%2F%5BworkflowId%5D%2Froute&page=%2Fapi%2Fhooks%2F%5BworkflowId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhooks%2F%5BworkflowId%5D%2Froute.ts&appDir=C%3A%5CWeb_dev%5CAI%5Cflow%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWeb_dev%5CAI%5Cflow&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();